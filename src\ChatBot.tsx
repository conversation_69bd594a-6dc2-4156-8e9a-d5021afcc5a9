import React, { useState } from "react";
import { Send, Mi<PERSON>, <PERSON><PERSON>, User, Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const DID_API_URL = "https://api.d-id.com/talks";

const ChatBot: React.FC = () => {
  const [text, setText] = useState<string>("");
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [messages, setMessages] = useState<
    Array<{ id: number; text: string; sender: "user" | "bot"; timestamp: Date }>
  >([]);

  const createTalk = async () => {
    if (text.trim() === "") return;

    // Add user message
    const userMessage = {
      id: Date.now(),
      text: text.trim(),
      sender: "user" as const,
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);

    setLoading(true);
    setVideoUrl(null);
    setText(""); // Clear input immediately

    const payload = {
      script: {
        type: "text",
        input: userMessage.text,
        // You can add voice_id, provider, style, etc.
      },
      // source_url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSpUyY77jqPWaZyIzr1aP-nErZUNeuyHcgoyQ&s', // Optional
    };

    try {
      const response = await fetch(DID_API_URL, {
        method: "POST",
        headers: {
          Authorization: `Basic ${btoa(`${import.meta.env.VITE_DID_API_KEY}`)}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to create talk: ${response.statusText}`);
      }

      const data = await response.json();
      const talkId = data.id;

      let videoUrlResponse: string | null = null;

      while (!videoUrlResponse) {
        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
          headers: {
            Authorization: `Basic ${btoa(
              `${import.meta.env.VITE_DID_API_KEY}`
            )}`,
            "Content-Type": "application/json",
          },
        });

        const pollData = await pollResp.json();

        if (pollData.status === "done" && pollData.result_url) {
          videoUrlResponse = pollData.result_url;
        } else {
          await new Promise((res) => setTimeout(res, 3000));
        }
      }

      setVideoUrl(videoUrlResponse);

      // Add bot response message
      const botMessage = {
        id: Date.now() + 1,
        text: userMessage.text,
        sender: "bot" as const,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    } catch (err: any) {
      console.error(err);
      alert("Error generating video: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    createTalk();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      createTalk();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl h-[80vh] bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden flex">
        {/* Left Side - Chat Interface */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 px-6 py-4 flex items-center space-x-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-white">
                AI Speaking Assistant
              </h1>
              <p className="text-blue-100 text-sm">
                Powered by D-ID Technology
              </p>
            </div>
            <div className="ml-auto flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white/80 text-sm">Online</span>
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-gradient-to-b from-gray-50/50 to-white/50">
            <AnimatePresence>
              {messages.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex flex-col items-center justify-center h-full text-center"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mb-4">
                    <Bot className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">
                    Welcome to AI Speaking Assistant
                  </h3>
                  <p className="text-gray-500 max-w-md">
                    Start a conversation by typing your message below. I'll
                    respond with a personalized video message!
                  </p>
                </motion.div>
              ) : (
                messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className={`flex ${
                      message.sender === "user"
                        ? "justify-end"
                        : "justify-start"
                    }`}
                  >
                    <div
                      className={`flex items-start space-x-3 max-w-[70%] ${
                        message.sender === "user"
                          ? "flex-row-reverse space-x-reverse"
                          : ""
                      }`}
                    >
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                          message.sender === "user"
                            ? "bg-gradient-to-br from-blue-500 to-indigo-600"
                            : "bg-gradient-to-br from-purple-500 to-pink-600"
                        }`}
                      >
                        {message.sender === "user" ? (
                          <User className="w-4 h-4 text-white" />
                        ) : (
                          <Bot className="w-4 h-4 text-white" />
                        )}
                      </div>
                      <div
                        className={`rounded-2xl px-4 py-3 ${
                          message.sender === "user"
                            ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                            : "bg-white border border-gray-200 text-gray-800 shadow-sm"
                        }`}
                      >
                        <p className="text-sm leading-relaxed">
                          {message.text}
                        </p>
                        <p
                          className={`text-xs mt-1 ${
                            message.sender === "user"
                              ? "text-blue-100"
                              : "text-gray-400"
                          }`}
                        >
                          {message.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </AnimatePresence>

            {loading && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="flex items-start space-x-3 max-w-[70%]">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                      <span className="text-sm text-gray-600">
                        Creating your video response...
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Input Area */}
          <div className="border-t border-gray-200 bg-white/70 backdrop-blur-sm p-4">
            <form onSubmit={handleSubmit} className="flex items-end space-x-3">
              <div className="flex-1 relative">
                <textarea
                  className="w-full border-2 border-gray-200 rounded-2xl px-4 py-3 pr-12 resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 transition-all duration-200 placeholder-gray-400 bg-white/80 backdrop-blur-sm"
                  rows={1}
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your message here... (Press Enter to send)"
                  disabled={loading}
                  style={{ minHeight: "44px", maxHeight: "120px" }}
                />
                <div className="absolute bottom-2 right-3 text-gray-400 text-xs">
                  {text.length}/500
                </div>
              </div>

              <motion.button
                type="submit"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`p-3 rounded-2xl font-medium transition-all duration-200 flex items-center justify-center mb-3  ${
                  loading || text.trim() === ""
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl"
                }`}
                disabled={loading || text.trim() === ""}
              >
                {loading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </motion.button>
            </form>
          </div>
        </div>

        {/* Right Side - Avatar */}
        <div className="w-80 bg-gradient-to-br from-indigo-50 to-purple-50 border-l border-gray-200/50 flex flex-col items-center justify-center p-8">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            {videoUrl ? (
              <div className="relative">
                <video
                  src={videoUrl}
                  controls
                  autoPlay
                  className="w-64 h-64 rounded-full object-cover border-4 border-white shadow-2xl"
                  style={{ aspectRatio: "1/1" }}
                />
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -bottom-3 -right-3 w-10 h-10 bg-green-500 rounded-full border-4 border-white flex items-center justify-center shadow-lg"
                >
                  <div className="w-3.5 h-3.5 bg-white rounded-full animate-pulse"></div>
                </motion.div>
              </div>
            ) : (
              <div className="w-64 h-64 rounded-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex items-center justify-center shadow-2xl border-4 border-white">
                <Bot className="w-28 h-28 text-white" />
              </div>
            )}
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="mt-6 text-center"
          >
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              AI Assistant
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Ready to help you with personalized video responses
            </p>

            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Active</span>
            </div>
          </motion.div>

          {loading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mt-6 flex flex-col items-center space-y-3"
            >
              <div className="flex items-center space-x-2 text-indigo-600">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span className="text-sm font-medium">Processing...</span>
              </div>
              <div className="w-32 h-1 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 3, repeat: Infinity }}
                />
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
