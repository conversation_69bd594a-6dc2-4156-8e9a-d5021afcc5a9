import React, { useState } from 'react';

const DID_API_URL = 'https://api.d-id.com/talks';

const ChatBot: React.FC = () => {
  const [text, setText] = useState<string>('');
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const createTalk = async () => {
    setLoading(true);
    setVideoUrl(null);

    const payload = {
      script: {
        type: 'text',
        input: text,
        // You can add voice_id, provider, style, etc.
      },
      // source_url: 'https://your-avatar-url.png', // Optional
    };

    try {
      const response = await fetch(DID_API_URL, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${btoa(`${import.meta.env.VITE_DID_API_KEY}`)}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to create talk: ${response.statusText}`);
      }

      const data = await response.json();
      const talkId = data.id;

      let videoUrlResponse: string | null = null;

      while (!videoUrlResponse) {
        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
          headers: {
            Authorization: `Basic ${btoa(`${import.meta.env.VITE_DID_API_KEY}:${import.meta.env.VITE_DID_API_SECRET}`)}`,
            'Content-Type': 'application/json',
          },
        });

        const pollData = await pollResp.json();

        if (pollData.status === 'done' && pollData.result_url) {
          videoUrlResponse = pollData.result_url;
        } else {
          await new Promise((res) => setTimeout(res, 3000));
        }
      }

      setVideoUrl(videoUrlResponse);
    } catch (err: any) {
      console.error(err);
      alert('Error generating video: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (text.trim() === '') return;
    createTalk();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
          <h1 className="text-xl font-semibold text-white text-center">AI Speaking Assistant</h1>
        </div>

        {/* Avatar Section */}
        <div className="px-6 py-8 flex flex-col items-center">
          {videoUrl ? (
            <div className="relative">
              <video
                src={videoUrl}
                controls
                autoPlay
                className="w-32 h-32 rounded-full object-cover border-4 border-blue-200 shadow-lg"
                style={{ aspectRatio: '1/1' }}
              />
              <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              </div>
            </div>
          ) : (
            <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center shadow-lg">
              <div className="text-white text-4xl">🤖</div>
            </div>
          )}

          {loading && (
            <div className="mt-4 flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
              <span className="text-sm font-medium">Processing your message...</span>
            </div>
          )}
        </div>

        {/* Input Section */}
        <div className="px-6 pb-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <textarea
                className="w-full border-2 border-gray-200 rounded-xl p-4 pr-12 resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 placeholder-gray-400"
                rows={3}
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="Type your message here..."
                disabled={loading}
              />
              <div className="absolute bottom-3 right-3 text-gray-400 text-xs">
                {text.length}/500
              </div>
            </div>

            <button
              type="submit"
              className={`w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 ${
                loading || text.trim() === ''
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl'
              }`}
              disabled={loading || text.trim() === ''}
            >
              {loading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <span>🎤</span>
                  <span>Speak</span>
                </div>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
